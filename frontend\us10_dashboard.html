<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Resume Doctor.Ai</title>
    <link rel="stylesheet" href="/static/css/us10_styles.css">
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .suggestion-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            transition: all 0.2s ease;
        }

        .suggestion-item:hover {
            border-color: #475569;
            box-shadow: 0 4px 12px rgba(71, 85, 105, 0.1);
        }

        .suggestion-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 8px;
        }

        .suggestion-description {
            color: #6b7280;
            font-size: 14px;
            line-height: 1.5;
        }

        .suggestion-priority {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .priority-high {
            background: #fee2e2;
            color: #991b1b;
        }

        .priority-medium {
            background: #fef3c7;
            color: #92400e;
        }

        .priority-low {
            background: #dcfce7;
            color: #166534;
        }

        /* Responsive Design for New Dashboard */
        @media (max-width: 768px) {
            .dashboard-content > div[style*="grid-template-columns: 1fr 1fr"] {
                display: block !important;
            }

            .dashboard-content > div[style*="grid-template-columns: 1fr 1fr"] > div {
                margin-bottom: 20px;
            }

            #resumeDropZone {
                padding: 30px 15px !important;
            }

            #jobDescriptionText {
                height: 100px !important;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div>
                <h1>🤖 Resume Doctor.Ai</h1>
                <nav class="dashboard-nav">
                    <a href="#" class="nav-item active">📊 Dashboard</a>
                    <a href="/account" class="nav-item">⚙️ Account</a>
                </nav>
            </div>
            
            <div class="user-info">
                <span id="welcomeMessage">Welcome, GUEST</span>
                <button class="logout-btn" onclick="logout()">🔐 Login</button>
            </div>
        </div>
        
        <div class="dashboard-content">
            <h2 style="margin-bottom: 30px; color: #1f2937;">Welcome, <span id="userName">GUEST</span></h2>
            <p style="color: #6b7280; margin-bottom: 40px;">AI-Powered Resume Analysis & Job Matching Platform</p>
            
            <!-- New Dashboard Layout -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 40px;">
                <!-- Resume Upload Section -->
                <div style="background: #f8fafc; border: 2px dashed #e2e8f0; border-radius: 12px; padding: 30px; text-align: center; transition: all 0.2s ease;" id="resumeUploadSection">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 20px;">
                        <div style="font-size: 24px;">📋</div>
                        <h3 style="color: #1f2937; margin: 0; font-size: 18px; font-weight: 600;">Resume</h3>
                        <button style="background: #6b7280; color: white; border: none; border-radius: 6px; padding: 4px 12px; font-size: 12px; cursor: pointer;" onclick="toggleResumeSelection()">Select Saved</button>
                    </div>

                    <!-- Resume Selection Mode Toggle -->
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; gap: 10px; justify-content: center; margin-bottom: 15px;">
                            <button id="uploadResumeTab" onclick="switchResumeMode('upload')" style="background: #3b82f6; color: white; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; cursor: pointer;">
                                📤 Upload New
                            </button>
                            <button id="selectResumeTab" onclick="switchResumeMode('select')" style="background: #6b7280; color: white; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; cursor: pointer;">
                                📋 Select Saved
                            </button>
                        </div>
                    </div>

                    <!-- Upload Mode -->
                    <div id="resumeUploadMode">
                        <div id="resumeDropZone" style="border: 2px dashed #cbd5e1; border-radius: 8px; padding: 40px 20px; margin-bottom: 20px; cursor: pointer; transition: all 0.2s ease;">
                            <div style="font-size: 48px; margin-bottom: 16px; color: #94a3b8;">📁</div>
                            <div style="color: #64748b; font-size: 16px; margin-bottom: 8px;">Drag & Drop or Upload</div>
                            <div style="color: #94a3b8; font-size: 14px;">Supports PDF, DOC, DOCX files</div>
                            <input type="file" id="resumeFileInput" accept=".pdf,.doc,.docx" style="display: none;">
                        </div>
                    </div>

                    <!-- Select Mode -->
                    <div id="resumeSelectMode" style="display: none;">
                        <div style="margin-bottom: 15px;">
                            <select id="savedResumeDropdown" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; background: white;">
                                <option value="">Select a saved resume...</option>
                            </select>
                        </div>
                        <div id="selectedResumeInfo" style="display: none; background: white; border: 1px solid #d1d5db; border-radius: 6px; padding: 15px; text-align: left;">
                            <div style="font-weight: 600; color: #1f2937; margin-bottom: 8px;" id="selectedResumeTitle"></div>
                            <div style="color: #6b7280; font-size: 14px;" id="selectedResumeMeta"></div>
                        </div>
                    </div>

                </div>

                <!-- Job Description Section -->
                <div style="background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 12px; padding: 30px;">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 20px;">
                        <div style="font-size: 24px;">📄</div>
                        <h3 style="color: #1f2937; margin: 0; font-size: 18px; font-weight: 600;">Job Description</h3>
                        <button style="background: #6b7280; color: white; border: none; border-radius: 6px; padding: 4px 12px; font-size: 12px; cursor: pointer;" onclick="toggleJobDescriptionSelection()">Select Saved</button>
                    </div>

                    <!-- Job Description Selection Mode Toggle -->
                    <div style="margin-bottom: 20px;">
                        <div style="display: flex; gap: 10px; justify-content: center; margin-bottom: 15px;">
                            <button id="enterJDTab" onclick="switchJDMode('enter')" style="background: #3b82f6; color: white; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; cursor: pointer;">
                                ✏️ Enter New
                            </button>
                            <button id="selectJDTab" onclick="switchJDMode('select')" style="background: #6b7280; color: white; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; cursor: pointer;">
                                📄 Select Saved
                            </button>
                        </div>
                    </div>

                    <!-- Enter Mode -->
                    <div id="jobDescriptionEnterMode">
                        <div style="margin-bottom: 16px;">
                            <div style="color: #374151; font-weight: 600; margin-bottom: 8px;">Example:</div>
                            <div style="background: white; border: 1px solid #d1d5db; border-radius: 6px; padding: 12px; font-size: 14px; color: #4b5563; max-height: 120px; overflow-y: auto;">
                                We are seeking a talented Software Engineer to join our dynamic team. The ideal candidate will have experience in:<br><br>
                                • Python, JavaScript, and modern web frameworks<br>
                                • Database design and optimization<br>
                                • Agile development methodologies<br>
                                • Cloud platforms (AWS, Azure, GCP)
                            </div>
                        </div>

                        <textarea id="jobDescriptionText" placeholder="Paste your job description here..." style="width: 100%; height: 120px; border: 1px solid #d1d5db; border-radius: 6px; padding: 12px; font-size: 14px; resize: vertical; font-family: inherit;"></textarea>

                        <!-- Save Job Description Button -->
                        <div style="margin-top: 12px;">
                            <button id="saveJobDescriptionBtn" onclick="manualSaveJobDescription()" style="background: #059669; color: white; border: none; border-radius: 6px; padding: 8px 16px; font-size: 14px; cursor: pointer; opacity: 0.5;" disabled>
                                💾 Save & Extract Keywords
                            </button>
                        </div>
                    </div>

                    <!-- Select Mode -->
                    <div id="jobDescriptionSelectMode" style="display: none;">
                        <div style="margin-bottom: 15px;">
                            <select id="savedJDDropdown" style="width: 100%; padding: 12px; border: 1px solid #d1d5db; border-radius: 6px; font-size: 14px; background: white;">
                                <option value="">Select a saved job description...</option>
                            </select>
                        </div>
                        <div id="selectedJDInfo" style="display: none; background: white; border: 1px solid #d1d5db; border-radius: 6px; padding: 15px; text-align: left;">
                            <div style="font-weight: 600; color: #1f2937; margin-bottom: 8px;" id="selectedJDTitle"></div>
                            <div style="color: #6b7280; font-size: 14px; margin-bottom: 10px;" id="selectedJDMeta"></div>
                            <div style="color: #4b5563; font-size: 14px; max-height: 100px; overflow-y: auto; background: #f9fafb; padding: 10px; border-radius: 4px;" id="selectedJDPreview"></div>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Generate Suggestions Buttons -->
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 40px;">
                <button id="generateBasicBtn" style="padding: 16px 24px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; background: linear-gradient(135deg, #475569 0%, #64748b 100%); color: white; display: flex; align-items: center; justify-content: center; gap: 8px;" onclick="generateBasicSuggestions()">
                    <span style="font-size: 20px;">⭐</span>
                    Generate Basic Suggestions
                </button>
                <button id="generatePremiumBtn" style="padding: 16px 24px; border: none; border-radius: 8px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.2s ease; background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%); color: white; display: flex; align-items: center; justify-content: center; gap: 8px;" onclick="generatePremiumSuggestions()">
                    <span style="font-size: 20px;">💎</span>
                    Generate Premium Suggestions
                </button>
            </div>

            <!-- Alert Container -->
            <div id="alertContainer" style="margin-bottom: 20px;"></div>

            <!-- Loading Indicator -->
            <div id="loadingIndicator" style="display: none; text-align: center; margin: 40px 0;">
                <div style="display: inline-block; width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #475569; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                <div style="margin-top: 16px; color: #6b7280; font-size: 16px;">Generating suggestions...</div>
            </div>

            <!-- Suggestions Container -->
            <div id="suggestionsContainer" style="display: none;">
                <!-- Matching Score Section -->
                <div id="matchingScoreSection" style="background: #e0f2fe; border-radius: 12px; padding: 30px; margin-bottom: 30px; border: 2px solid #0ea5e9;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; padding-bottom: 16px; border-bottom: 2px solid #0ea5e9;">
                        <h3 style="color: #1f2937; font-size: 20px; font-weight: 600; margin: 0;">
                            <span style="font-size: 24px; margin-right: 8px;">📊</span>
                            Resume-Job Match Analysis
                        </h3>
                    </div>
                    <div id="matchingScoreContent">
                        <!-- Score content will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Basic Suggestions -->
                <div id="basicSuggestionsCard" style="background: #f8fafc; border-radius: 12px; padding: 30px; margin-bottom: 30px; display: none;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="color: #1f2937; font-size: 20px; font-weight: 600; margin: 0;">
                            <span style="font-size: 24px; margin-right: 8px; color: #475569;">💡</span>
                            Basic Suggestions
                        </h3>
                        <span id="basicCount" style="background: #475569; color: white; padding: 4px 12px; border-radius: 20px; font-size: 14px; font-weight: 600;">0</span>
                    </div>
                    <div id="basicSuggestionsBody">
                        <!-- Basic suggestions will be populated here -->
                    </div>
                </div>

                <!-- Premium Suggestions -->
                <div id="premiumSuggestionsCard" style="background: #fef3c7; border-radius: 12px; padding: 30px; margin-bottom: 30px; display: none;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3 style="color: #1f2937; font-size: 20px; font-weight: 600; margin: 0;">
                            <span style="font-size: 24px; margin-right: 8px; color: #f59e0b;">👑</span>
                            Premium Suggestions
                        </h3>
                        <span id="premiumCount" style="background: #f59e0b; color: white; padding: 4px 12px; border-radius: 20px; font-size: 14px; font-weight: 600;">0</span>
                    </div>
                    <div id="premiumSuggestionsBody">
                        <!-- Premium suggestions will be populated here -->
                    </div>
                </div>
            </div>
            
            <!-- Stats Section -->
            <div style="background: #f8fafc; border-radius: 12px; padding: 30px; margin-top: 30px;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">📊 Dashboard</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #475569;" id="resumeCount">0</div>
                        <div style="color: #6b7280;">Resumes</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #475569;" id="jdCount">0</div>
                        <div style="color: #6b7280;">Job Descriptions</div>
                    </div>
                    <div style="text-align: center;">
                        <div style="font-size: 32px; font-weight: bold; color: #475569;" id="scanCount">0</div>
                        <div style="color: #6b7280;">Scans</div>
                    </div>
                </div>
            </div>
            
            <!-- Job Description List Section -->
            <div class="jd-list" id="jdListSection" style="display: none;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">📄 Your Job Descriptions</h3>
                <div id="jdList"></div>
            </div>

            <!-- Resume List Section -->
            <div class="resume-list" id="resumeListSection" style="display: none;">
                <h3 style="color: #1f2937; margin-bottom: 20px;">📋 Your Resumes</h3>
                <div id="resumeList"></div>
            </div>
            
            <!-- Scan History Section -->
            <div style="margin-top: 30px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h3 style="color: #1f2937; margin: 0;">📈 Recent Scan History</h3>
                    <div style="display: flex; gap: 10px;">
                        <select id="scoreFilter" onchange="filterHistory()" style="padding: 8px; border: 1px solid #d1d5db; border-radius: 6px;">
                            <option value="">All Scores</option>
                            <option value="excellent">Excellent (80-100%)</option>
                            <option value="good">Good (60-79%)</option>
                            <option value="fair">Fair (40-59%)</option>
                            <option value="poor">Poor (0-39%)</option>
                        </select>
                        <button onclick="refreshHistory()" style="padding: 8px 12px; background: #475569; color: white; border: none; border-radius: 6px; cursor: pointer;">
                            🔄 Refresh
                        </button>
                    </div>
                </div>

                <div id="scanHistoryContainer">
                    <div id="scanHistoryLoading" style="text-align: center; padding: 40px; color: #6b7280;">
                        <div style="font-size: 24px; margin-bottom: 10px;">⏳</div>
                        Loading scan history...
                    </div>
                    <div id="scanHistoryEmpty" style="display: none; text-align: center; padding: 40px; color: #6b7280;">
                        <div style="font-size: 48px; margin-bottom: 20px;">📊</div>
                        <h4 style="margin: 0 0 10px 0;">No scan history yet</h4>
                        <p style="margin: 0;">Upload a resume and job description to start scanning!</p>
                    </div>
                    <div id="scanHistoryList"></div>
                </div>

                <div id="historyPagination" style="display: none; text-align: center; margin-top: 20px;">
                    <button id="prevPageBtn" onclick="changePage(-1)" style="padding: 8px 16px; margin: 0 5px; background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                        ← Previous
                    </button>
                    <span id="pageInfo" style="margin: 0 15px; color: #6b7280;"></span>
                    <button id="nextPageBtn" onclick="changePage(1)" style="padding: 8px 16px; margin: 0 5px; background: #f3f4f6; border: 1px solid #d1d5db; border-radius: 6px; cursor: pointer;">
                        Next →
                    </button>
                </div>
            </div>


        </div>
    </div>

    <script src="/static/js/us10_dashboard.js"></script>
</body>
</html>
