<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dr. Resume - AI-Powered Resume Scanner & Job Matching Platform</title>
    <link rel="stylesheet" href="/static/css/us10_styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                🩺 Welcome to Dr. Resume
            </h1>
            <p class="subtitle">AI-Powered Resume Scanner & Job Matching Platform</p>
        </div>
        
        <div class="content">
            <p class="welcome-text">
                Get started by creating an account or signing in to access all features.
            </p>
            
            <a href="/register" class="btn btn-primary">
                👤 Create Account
            </a>
            
            <a href="/login" class="btn btn-secondary">
                🔐 Sign In
            </a>
        </div>
    </div>

    <script>
        // Check if user is already logged in
        const token = localStorage.getItem('dr_resume_token');
        if (token) {
            // Redirect to dashboard if already logged in
            window.location.href = '/dashboard';
        }
        
        // Simple page analytics
        console.log('🩺 Dr. Resume Landing Page Loaded (US-05)');
        console.log('🔍 US-05: Keyword Parsing & Extraction System');
        
        // Add click tracking for buttons
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                console.log(`Button clicked: ${this.textContent.trim()}`);
            });
        });
    </script>
</body>
</html>
