# Dr. Resume US-10 Complete Requirements: Local AI + Full Integration
# All dependencies for the complete Dr. Resume application

# Core Flask Framework
Flask==2.3.3
Flask-CORS==4.0.0

# Database & ORM
SQLAlchemy==2.0.21
Flask-SQLAlchemy==3.0.5

# Authentication & Security
Flask-JWT-Extended==4.5.3
Werkzeug==2.3.7
bcrypt==4.0.1

# File Processing
PyPDF2==3.0.1
python-docx==0.8.11

# NLP & Text Analysis (REQUIRED for Local AI)
nltk==3.8.1
spacy>=3.6.1
scikit-learn==1.3.0
numpy==1.24.3

# Similarity & Matching Algorithms
fuzzywuzzy==0.18.0
python-Levenshtein==0.21.1

# Environment & Configuration
python-dotenv==1.0.0

# AI & Premium Features (Optional - Local AI works without this)
openai>=1.0.0

# Development & Testing
pytest==7.4.2
pytest-flask==1.2.0

# Additional dependencies for Local AI
typing-extensions>=4.0.0
